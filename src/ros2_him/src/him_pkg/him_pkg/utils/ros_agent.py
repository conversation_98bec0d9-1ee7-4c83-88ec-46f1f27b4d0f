import time
import numpy as np
import torch
from lowlevel_msg.msg import LowCmd
import threading
from .cpp_lowcmd_node import is_cpp_available, create_cpp_publisher

def class_to_dict(obj) -> dict:
    if not hasattr(obj, "__dict__"):
        return obj
    result = {}
    for key in dir(obj):
        if key.startswith("_") or key == "terrain":
            continue
        element = []
        val = getattr(obj, key)
        if isinstance(val, list):
            for item in val:
                element.append(class_to_dict(item))
        else:
            element = class_to_dict(val)
        result[key] = element
    return result


class ROSAgent():
    def __init__(self, cfg, se, command_profile, runtime_env="mujoco", use_cpp_publisher=True):
        if not isinstance(cfg, dict):
            cfg = class_to_dict(cfg)
        self.cfg = cfg
        self.se = se
        self.command_profile = command_profile
        self.runtime_env = runtime_env
        
        # 初始化 C++ publisher
        self.use_cpp_publisher = use_cpp_publisher and is_cpp_available()
        self.cpp_publisher = None
        if self.use_cpp_publisher:
            print("Using C++ publisher for better performance")
        else:
            print("Using Python publisher (C++ not available or disabled)")

        self.dt = self.cfg["control"]["decimation"] * self.cfg["sim"]["dt"]
        self.timestep = 0

        self.num_obs = self.cfg["env"]["num_one_step_observations"]
        self.num_envs = 1
        self.num_privileged_obs = self.cfg["env"]["num_one_step_privileged_obs"]
        self.num_actions = self.cfg["env"]["num_actions"]
        self.num_commands = 3  # 修改为固定值3，因为我们只使用前三个命令：cmd_x, cmd_y, cmd_yaw
        
        # 安全检测CUDA可用性
        cuda_available = False
        try:
            cuda_available = torch.cuda.is_available()
            if cuda_available:
                # 尝试一个简单的CUDA操作来验证PyTorch是否支持CUDA
                test_tensor = torch.zeros(1, device="cuda")
                self.device = 'cuda'
            else:
                self.device = 'cpu'
                print("CUDA不可用，使用CPU")
        except (AssertionError, RuntimeError) as e:
            self.device = 'cpu'
            print(f"CUDA错误: {e}，使用CPU")
        
        print(f"使用设备: {self.device}")

        if "obs_scales" in self.cfg.keys():
            self.obs_scales = self.cfg["obs_scales"]
        else:
            self.obs_scales = self.cfg["normalization"]["obs_scales"]

        self.commands_scale = np.array(
            [self.obs_scales["lin_vel"], self.obs_scales["lin_vel"],
             self.obs_scales["ang_vel"]
            #  self.obs_scales["footswing_height_cmd"], self.obs_scales["body_pitch_cmd"],
            #  # 0, self.obs_scales["body_pitch_cmd"],
            #  self.obs_scales["body_roll_cmd"], self.obs_scales["stance_width_cmd"],
            #  self.obs_scales["stance_length_cmd"], self.obs_scales["aux_reward_cmd"], 1, 1, 1, 1, 1, 1
             ])[:self.num_commands]


        joint_names = [
            "FL_hip_joint", "FL_thigh_joint", "FL_calf_joint",
            "FR_hip_joint", "FR_thigh_joint", "FR_calf_joint",
            "RL_hip_joint", "RL_thigh_joint", "RL_calf_joint",
            "RR_hip_joint", "RR_thigh_joint", "RR_calf_joint", ]
        self.default_dof_pos = np.array([self.cfg["init_state"]["default_joint_angles"][name] for name in joint_names])
        try:
            self.default_dof_pos_scale = np.array([self.cfg["init_state"]["default_hip_scales"], self.cfg["init_state"]["default_thigh_scales"], self.cfg["init_state"]["default_calf_scales"],
                                                   self.cfg["init_state"]["default_hip_scales"], self.cfg["init_state"]["default_thigh_scales"], self.cfg["init_state"]["default_calf_scales"],
                                                   self.cfg["init_state"]["default_hip_scales"], self.cfg["init_state"]["default_thigh_scales"], self.cfg["init_state"]["default_calf_scales"],
                                                   self.cfg["init_state"]["default_hip_scales"], self.cfg["init_state"]["default_thigh_scales"], self.cfg["init_state"]["default_calf_scales"]])
        except KeyError:
            self.default_dof_pos_scale = np.ones(12)
        self.default_dof_pos = self.default_dof_pos * self.default_dof_pos_scale

        # self.p_gains = np.zeros(12)
        # self.d_gains = np.zeros(12)
        # for i in range(12):
        #     joint_name = joint_names[i]
        #     found = False
        #     for dof_name in self.cfg["control"]["stiffness"].keys():
        #         if dof_name in joint_name:
        #             self.p_gains[i] = self.cfg["control"]["stiffness"][dof_name]
        #             self.d_gains[i] = self.cfg["control"]["damping"][dof_name]
        #             found = True
        #     if not found:
        #         self.p_gains[i] = 0.
        #         self.d_gains[i] = 0.
        #         if self.cfg["control"]["control_type"] in ["P", "V"]:
        #             print(f"PD gain of joint {joint_name} were not defined, setting them to zero")

        # print(f"p_gains: {self.p_gains},  d_gains: {self.d_gains}")
        if self.runtime_env == "realbot":
            # self.p_gains = np.array([30.0, 30.0, 7.5,
            #                          30.0, 30.0, 7.5,
            #                          30.0, 30.0, 7.5,
            #                          30.0, 30.0, 7.5])
            # self.d_gains = np.array([1.0, 1.0, 0.25,
            #                          1.0, 1.0, 0.25,
            #                          1.0, 1.0, 0.25,
            #                          1.0, 1.0, 0.25])
            self.p_gains = np.array([40.0, 40.0, 10.0,
                                     40.0, 40.0, 10.0,
                                     40.0, 40.0, 10.0,
                                     40.0, 40.0, 10.0])
            self.d_gains = np.array([1.0, 1.0, 0.25,
                                     1.0, 1.0, 0.25,
                                     1.0, 1.0, 0.25,
                                     1.0, 1.0, 0.25])
        else:
            # mujoco环境下设置相同的kp/kd，但是小腿关节不除以4
            # self.p_gains = np.array([30.0, 30.0, 30.0,
            #                          30.0, 30.0, 30.0,
            #                          30.0, 30.0, 30.0,
            #                          30.0, 30.0, 30.0])
            # self.d_gains = np.array([1.0, 1.0, 1.0,
            #                          1.0, 1.0, 1.0,
            #                          1.0, 1.0, 1.0,
            #                          1.0, 1.0, 1.0])
            # self.p_gains = np.array([40.0, 40.0, 40.0,
            #                          40.0, 40.0, 40.0,
            #                          40.0, 40.0, 40.0,
            #                          40.0, 40.0, 40.0])
            self.p_gains = np.array([40.0, 40.0, 40.0,
                                     40.0, 40.0, 40.0,
                                     40.0, 40.0, 40.0,
                                     40.0, 40.0, 40.0])
            self.d_gains = np.array([1.0, 1.0, 1.0,
                                     1.0, 1.0, 1.0,
                                     1.0, 1.0, 1.0,
                                     1.0, 1.0, 1.0])

        self.commands = np.zeros((1, self.num_commands))
        self.actions = torch.zeros(12)
        self.last_actions = torch.zeros(12)
        self.gravity_vector = np.zeros(3)
        self.dof_pos = np.zeros(12)
        self.tau_est = np.zeros(12)
        self.dof_vel = np.zeros(12)
        self.body_linear_vel = np.zeros(3)
        self.body_angular_vel = np.zeros(3)
        # self.body_angular_vel_imu = np.zeros(3)
        self.joint_pos_target = np.zeros(12)
        self.joint_vel_target = np.zeros(12)
        self.torques = np.zeros(12)
        self.contact_state = np.ones(4)

        self.joint_idxs = self.se.joint_idxs

        self.gait_indices = torch.zeros(self.num_envs, dtype=torch.float)
        self.clock_inputs = torch.zeros(self.num_envs, 4, dtype=torch.float)

        self.is_logging = False

        self.cmd_id = 0
        self.ts_infer_input_state = 0
        self.input_state_id = 0
        self.input_state_rela_cmd_id = 0
        self.ts_before_infer = 0
        self.ts_after_infer = 0

        self.cmd_smoothing_ratio = 0.95
        self._is_cleaned_up = False
    
    def __del__(self):
        """析构函数，确保资源清理"""
        self.cleanup()
    
    def cleanup(self):
        """清理所有资源"""
        if self._is_cleaned_up:
            return
            
        try:
            # 清理C++发布者
            if hasattr(self, 'use_cpp_publisher') and self.use_cpp_publisher and hasattr(self, 'cpp_publisher') and self.cpp_publisher:
                try:
                    self.cpp_publisher.stop()
                    self.cpp_publisher = None
                except Exception as e:
                    print(f"Error stopping C++ publisher: {e}")
            
            # 清理torch张量和GPU缓存
            try:
                if hasattr(self, 'actions'):
                    del self.actions
                if hasattr(self, 'last_actions'):
                    del self.last_actions
                    
                # 如果使用GPU，清理GPU缓存
                if hasattr(self, 'device') and self.device == "cuda" and torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except Exception as e:
                print(f"Error cleaning torch resources: {e}")
            
        except Exception as e:
            print(f"Error in ROSAgent cleanup: {e}")
        finally:
            self._is_cleaned_up = True

    def set_action_publisher(self, action_publisher, cmd_topic="/rt/lowcmd"):
        """设置动作发布者"""
        # 如果已经有C++发布者，先清理
        if hasattr(self, 'cpp_publisher') and self.cpp_publisher:
            try:
                self.cpp_publisher.stop()
                self.cpp_publisher = None
            except Exception:
                pass
        
        self.action_publisher = action_publisher
        
        # 如果使用 C++ publisher，创建并启动它
        if self.use_cpp_publisher:
            try:
                self.cpp_publisher = create_cpp_publisher(cmd_topic)
                if self.cpp_publisher:
                    self.cpp_publisher.start()
                    print(f"C++ publisher started on topic: {cmd_topic}")
                else:
                    print("Failed to create C++ publisher, falling back to Python")
                    self.use_cpp_publisher = False
            except Exception as e:
                print(f"Error creating C++ publisher: {e}, falling back to Python")
                self.use_cpp_publisher = False

    def set_logging(self, is_logging):
        self.is_logging = is_logging

    def get_obs(self):

        cmds, reset_timer = self.command_profile.get_command(self.timestep * self.dt)
        if isinstance(cmds, torch.Tensor):
            cmds = cmds.cpu().numpy()
        if len(cmds.shape) == 1:
            cmds = cmds.reshape(1, -1)
        self.commands[:, :] = self.cmd_smoothing_ratio * self.commands[:, :] + (1 - self.cmd_smoothing_ratio) * cmds[:, :self.num_commands]
        if reset_timer:
            self.reset_gait_indices()
        #else:
        #    self.commands[:, 0:3] = self.command_profile.get_command(self.timestep * self.dt)[0:3]

        wtw_obs = self.se.get_wtw_obs()
        self.gravity_vector = wtw_obs['gravity_vector']
        self.dof_pos = wtw_obs['dof_pos']
        self.tau_est = wtw_obs['tau_est']
        self.dof_vel = wtw_obs['dof_vel']
        self.body_linear_vel = wtw_obs['body_linear_vel']
        self.body_angular_vel = wtw_obs['body_angular_vel']
        # self.body_angular_vel_imu = wtw_obs['body_angular_vel_imu']
        self.input_state_id = wtw_obs['input_state_id']
        self.input_state_rela_cmd_id = wtw_obs['input_state_rela_cmd_id']
        self.ts_infer_input_state = wtw_obs['ts_infer_input_state']

        ob = np.concatenate((self.commands[:,:3] * self.commands_scale, #0-2, [2,2,0.25]
                             self.body_angular_vel.reshape(1,-1)*self.obs_scales["ang_vel"],  #3-5, 0.25
                             self.gravity_vector.reshape(1, -1), #6-8, 1
                             (self.dof_pos - self.default_dof_pos).reshape(1, -1) * self.obs_scales["dof_pos"], #9-20, 1
                             self.dof_vel.reshape(1, -1) * self.obs_scales["dof_vel"],  #21-32, 0.05
                             torch.clip(self.actions, -self.cfg["normalization"]["clip_actions"],
                                        self.cfg["normalization"]["clip_actions"]).cpu().detach().numpy().reshape(1, -1)
                             ), axis=1) #33-44, 1
        ob = np.clip(ob, -self.cfg["normalization"]["clip_observations"], self.cfg["normalization"]["clip_observations"])

        # if self.cfg["env"]["observe_two_prev_actions"]:
        #     ob = np.concatenate((ob,
        #                     self.last_actions.cpu().detach().numpy().reshape(1, -1)), axis=1)

        # if self.cfg["env"]["observe_clock_inputs"]:
        #     ob = np.concatenate((ob,
        #                     self.clock_inputs), axis=1)
        #     # print(self.clock_inputs)

        # if self.cfg["env"]["observe_vel"]:
        #     ob = np.concatenate(
        #         (self.body_linear_vel.reshape(1, -1) * self.obs_scales["lin_vel"],
        #          self.body_angular_vel.reshape(1, -1) * self.obs_scales["ang_vel"],
        #          ob), axis=1)

        # if self.cfg["env"]["observe_only_lin_vel"]:
        #     ob = np.concatenate(
        #         (self.body_linear_vel.reshape(1, -1) * self.obs_scales["lin_vel"],
        #          ob), axis=1)

        # if self.cfg["env"]["observe_yaw"]:
        #     heading = self.se.get_yaw()
        #     ob = np.concatenate((ob, heading.reshape(1, -1)), axis=-1)

        # self.contact_state = self.se.get_contact_state()
        # if "observe_contact_states" in self.cfg["env"].keys() and self.cfg["env"]["observe_contact_states"]:
        #     ob = np.concatenate((ob, self.contact_state.reshape(1, -1)), axis=-1)

        # if "terrain" in self.cfg.keys() and self.cfg["terrain"]["measure_heights"]:
        #     robot_height = 0.25
        #     self.measured_heights = np.zeros(
        #         (len(self.cfg["terrain"]["measured_points_x"]), len(self.cfg["terrain"]["measured_points_y"]))).reshape(
        #         1, -1)
        #     heights = np.clip(robot_height - 0.5 - self.measured_heights, -1, 1.) * self.obs_scales["height_measurements"]
        #     ob = np.concatenate((ob, heights), axis=1)

        # 安全创建张量，避免CUDA错误
        try:
            tensor_ob = torch.tensor(ob, device=self.device).float()
        except (RuntimeError, AssertionError) as e:
            print(f"无法在 {self.device} 上创建张量: {e}")
            print("回退到 CPU 设备")
            self.device = "cpu"
            tensor_ob = torch.tensor(ob, device="cpu").float()
        
        return tensor_ob

    def get_privileged_observations(self):
        return None

    def publish_action(self, action, hard_reset=False):
        # 计算关节目标位置
        self.joint_pos_target = \
            (action[0, :12].detach().cpu().numpy() * self.cfg["control"]["action_scale"]).flatten()
        self.joint_pos_target[[0, 3, 6, 9]] *= self.cfg["control"]["hip_reduction"]
        self.joint_pos_target += self.default_dof_pos
        joint_pos_target = self.joint_pos_target[self.joint_idxs]
        self.joint_vel_target = np.zeros(12)

        # 更新命令ID和时间戳
        self.cmd_id = (self.cmd_id + 1) % 50000
        self.ts_after_infer = int(time.time() * 1000000)

        # 计算扭矩
        self.torques = (self.joint_pos_target - self.dof_pos) * self.p_gains + (self.joint_vel_target - self.dof_vel) * self.d_gains

        # 选择使用 C++ 或 Python publisher
        if self.use_cpp_publisher and self.cpp_publisher and self.cpp_publisher.is_running():
            try:
                # 使用 C++ publisher
                self.cpp_publisher.publish_action(
                    joint_pos_target,
                    self.joint_vel_target,
                    self.p_gains,
                    self.d_gains,
                    self.cmd_id,
                    self.input_state_id,
                    self.ts_infer_input_state,
                    self.ts_before_infer,
                    self.ts_after_infer
                )
            except Exception as e:
                print(f"Error using C++ publisher: {e}, falling back to Python")
                self.use_cpp_publisher = False
                self._publish_action_python(joint_pos_target)
        else:
            # 使用 Python publisher
            self._publish_action_python(joint_pos_target)

    def _publish_action_python(self, joint_pos_target):
        """使用 Python ROS2 publisher 发布动作"""
        msg = LowCmd()

        for i in range(12):
            msg.motor_cmd[i].q = float(joint_pos_target[i])
            msg.motor_cmd[i].dq = float(self.joint_vel_target[i])
            msg.motor_cmd[i].kp = float(self.p_gains[i])
            msg.motor_cmd[i].kd = float(self.d_gains[i])
            msg.motor_cmd[i].tau = 0.0
        
        msg.cmd_id = self.cmd_id
        msg.input_state_id = self.input_state_id
        msg.ts_infer_input_state = self.ts_infer_input_state
        msg.ts_before_infer = self.ts_before_infer
        msg.ts_after_infer = self.ts_after_infer

        self.action_publisher.publish(msg)


    def reset(self):
        self.actions = torch.zeros(12)
        self.time = time.time()
        self.timestep = 0
        return self.get_obs()

    def reset_gait_indices(self):
        self.gait_indices = torch.zeros(self.num_envs, dtype=torch.float)

    def step(self, actions, hard_reset=False):
        clip_actions = self.cfg["normalization"]["clip_actions"]
        self.last_actions = self.actions[:]
        self.actions = torch.clip(actions[0:1, :], -clip_actions, clip_actions)
        self.publish_action(self.actions, hard_reset=hard_reset)  # 更新了torque, dof_pos_target
        # current_thread = threading.current_thread()
        # thread_id = current_thread.ident
        if self.timestep % 100 == 0: print(f'model infer time: {(time.time() - self.time)} s,  state_id: {self.input_state_id}')
        # if (time.time() - self.time) >= 0.02: print(f'model infer time: {(time.time() - self.time)} s,  state_id: {self.input_state_id}, current_thread: {thread_id}')

        time.sleep(max(self.dt - (time.time() - self.time), 0))   # 持续发送相同的dof_pos_target
        if self.timestep % 100 == 0: print(f'frq: {1 / (time.time() - self.time)} Hz')
        self.time = time.time()
        obs = self.get_obs()   # 更新了 dof_pos, tau_est, dof_vel, state tag   

        if self.timestep % 100 == 0: 
            print(f'cmd_x: {self.commands[0, 0]}, cmd_y: {self.commands[0, 1]}, cmd_yaw: {self.commands[0, 2]}')

        # # clock accounting
        # frequencies = self.commands[:, 4]
        # phases = self.commands[:, 5]
        # offsets = self.commands[:, 6]
        # if self.num_commands == 8:
        #     bounds = 0
        #     durations = self.commands[:, 7]
        # else:
        #     bounds = self.commands[:, 7]
        #     durations = self.commands[:, 8]
        # self.gait_indices = torch.remainder(self.gait_indices + self.dt * frequencies, 1.0)

        # if "pacing_offset" in self.cfg["commands"] and self.cfg["commands"]["pacing_offset"]:
        #     self.foot_indices = [self.gait_indices + phases + offsets + bounds,
        #                          self.gait_indices + bounds,
        #                          self.gait_indices + offsets,
        #                          self.gait_indices + phases]
        # else:
        #     self.foot_indices = [self.gait_indices + phases + offsets + bounds,
        #                          self.gait_indices + offsets,
        #                          self.gait_indices + bounds,
        #                          self.gait_indices + phases]
        # self.clock_inputs[:, 0] = torch.sin(2 * np.pi * self.foot_indices[0])
        # self.clock_inputs[:, 1] = torch.sin(2 * np.pi * self.foot_indices[1])
        # self.clock_inputs[:, 2] = torch.sin(2 * np.pi * self.foot_indices[2])
        # self.clock_inputs[:, 3] = torch.sin(2 * np.pi * self.foot_indices[3])


        # images = {'front': self.se.get_camera_front(),
        #           'bottom': self.se.get_camera_bottom(),
        #           'rear': self.se.get_camera_rear(),
        #           'left': self.se.get_camera_left(),
        #           'right': self.se.get_camera_right()
        #           }
        # downscale_factor = 2
        # temporal_downscale = 3

        # for k, v in images.items():
        #     if images[k] is not None:
        #         images[k] = cv2.resize(images[k], dsize=(images[k].shape[0]//downscale_factor, images[k].shape[1]//downscale_factor), interpolation=cv2.INTER_CUBIC)
        #     if self.timestep % temporal_downscale != 0:
        #         images[k] = None
        # #print(self.commands)

        infos = {"joint_pos": self.dof_pos[np.newaxis, :],
                 "tau_est": self.tau_est[np.newaxis, :],
                 "joint_vel": self.dof_vel[np.newaxis, :],
                 "input_state_rela_cmd_id": self.input_state_rela_cmd_id,
                 "joint_pos_target": self.joint_pos_target[np.newaxis, :],
                 "cmd_id": (self.cmd_id - 1) % 50000,
                 "joint_vel_target": self.joint_vel_target[np.newaxis, :],
                 "body_linear_vel": self.body_linear_vel[np.newaxis, :],
                 "body_angular_vel": self.body_angular_vel[np.newaxis, :],
                #  "body_angular_vel_imu": self.body_angular_vel_imu[np.newaxis, :],
                 "contact_state": self.contact_state[np.newaxis, :],
                 "clock_inputs": self.clock_inputs[np.newaxis, :],
                 "body_linear_vel_cmd": self.commands[:, 0:2],
                 "body_angular_vel_cmd": self.commands[:, 2:],
                 "privileged_obs": None,
                #  "camera_image_front": images['front'],
                #  "camera_image_bottom": images['bottom'],
                #  "camera_image_rear": images['rear'],
                #  "camera_image_left": images['left'],
                #  "camera_image_right": images['right'],
                 }

        self.timestep += 1
        self.ts_before_infer = int(time.time() * 1000000)
        return obs, None, None, infos
