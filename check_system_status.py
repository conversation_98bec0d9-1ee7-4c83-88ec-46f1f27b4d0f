#!/usr/bin/env python3
"""
检查系统状态和话题连接的脚本
"""

import rclpy
from rclpy.node import Node
import subprocess
import time

class SystemStatusChecker(Node):
    def __init__(self):
        super().__init__('system_status_checker')
        self.get_logger().info("System Status Checker started")
    
    def check_topics(self):
        """检查相关话题的状态"""
        self.get_logger().info("=== 检查ROS2话题状态 ===")
        
        topics_to_check = [
            '/vel_cmd',
            '/joy',
            'stairsmodel_switch_trigger',
            'flatmodel_switch_trigger',
            '/joy_cmd'
        ]
        
        for topic in topics_to_check:
            try:
                result = subprocess.run(['ros2', 'topic', 'info', topic], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    self.get_logger().info(f"✅ 话题 {topic} 存在")
                    # 检查发布者和订阅者
                    lines = result.stdout.split('\n')
                    pub_count = 0
                    sub_count = 0
                    for line in lines:
                        if 'Publisher count:' in line:
                            pub_count = int(line.split(':')[1].strip())
                        elif 'Subscription count:' in line:
                            sub_count = int(line.split(':')[1].strip())
                    self.get_logger().info(f"   发布者: {pub_count}, 订阅者: {sub_count}")
                else:
                    self.get_logger().warn(f"❌ 话题 {topic} 不存在或无法访问")
            except Exception as e:
                self.get_logger().error(f"检查话题 {topic} 时出错: {e}")
    
    def check_nodes(self):
        """检查相关节点的状态"""
        self.get_logger().info("\n=== 检查ROS2节点状态 ===")
        
        try:
            result = subprocess.run(['ros2', 'node', 'list'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                nodes = result.stdout.strip().split('\n')
                relevant_nodes = [node for node in nodes if any(keyword in node.lower() 
                                for keyword in ['joy', 'him', 'model', 'switch'])]
                
                if relevant_nodes:
                    self.get_logger().info("找到相关节点:")
                    for node in relevant_nodes:
                        self.get_logger().info(f"  - {node}")
                else:
                    self.get_logger().warn("未找到相关节点")
                    self.get_logger().info("所有节点:")
                    for node in nodes:
                        self.get_logger().info(f"  - {node}")
            else:
                self.get_logger().error("无法获取节点列表")
        except Exception as e:
            self.get_logger().error(f"检查节点时出错: {e}")
    
    def monitor_vel_cmd_topic(self, duration=10):
        """监听 /vel_cmd 话题"""
        self.get_logger().info(f"\n=== 监听 /vel_cmd 话题 {duration} 秒 ===")
        
        try:
            # 使用 ros2 topic echo 监听话题
            process = subprocess.Popen(['ros2', 'topic', 'echo', '/vel_cmd'], 
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < duration:
                try:
                    line = process.stdout.readline()
                    if line:
                        if 'linear:' in line:
                            message_count += 1
                            self.get_logger().info(f"收到第 {message_count} 条速度消息")
                        elif 'x:' in line and 'linear' in process.stdout.readline():
                            # 提取 x 值
                            x_value = line.split(':')[1].strip()
                            self.get_logger().info(f"  vel_x = {x_value}")
                except:
                    break
            
            process.terminate()
            
            if message_count == 0:
                self.get_logger().warn("在监听期间未收到任何 /vel_cmd 消息")
            else:
                self.get_logger().info(f"总共收到 {message_count} 条消息")
                
        except Exception as e:
            self.get_logger().error(f"监听 /vel_cmd 话题时出错: {e}")

def main():
    rclpy.init()
    
    try:
        checker = SystemStatusChecker()
        
        # 检查系统状态
        checker.check_topics()
        checker.check_nodes()
        
        # 监听速度命令话题
        checker.monitor_vel_cmd_topic(10)
        
        checker.get_logger().info("\n=== 检查完成 ===")
        
    except KeyboardInterrupt:
        print("\n用户中断，退出...")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()

if __name__ == '__main__':
    main()
