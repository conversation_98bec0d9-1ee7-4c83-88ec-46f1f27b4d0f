#!/usr/bin/env python3
"""
调试速度切换功能的脚本
"""

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import Bool
import time
import sys

class VelocitySwitchDebugger(Node):
    def __init__(self):
        super().__init__('velocity_switch_debugger')
        
        # 创建发布者
        self.vel_publisher = self.create_publisher(Twist, '/vel_cmd', 10)
        
        # 创建订阅者监听模型切换话题
        self.stairs_sub = self.create_subscription(
            Bool, 'stairsmodel_switch_trigger', self.stairs_callback, 10
        )
        self.flat_sub = self.create_subscription(
            Bool, 'flatmodel_switch_trigger', self.flat_callback, 10
        )
        
        self.get_logger().info("Velocity Switch Debugger started")
        self.get_logger().info("Listening for model switch triggers...")
        
    def stairs_callback(self, msg):
        if msg.data:
            self.get_logger().info("🔥 DETECTED: Switch to model B (stairs) triggered!")
    
    def flat_callback(self, msg):
        if msg.data:
            self.get_logger().info("🟢 DETECTED: Switch to model A (flat) triggered!")
    
    def test_velocity_commands(self):
        """测试不同的速度命令"""
        
        # 等待发布者准备就绪
        time.sleep(1.0)
        
        test_cases = [
            {"name": "正速度测试", "vel_x": 0.5, "expected": "A模型"},
            {"name": "负速度测试", "vel_x": -0.3, "expected": "B模型"},
            {"name": "零速度测试", "vel_x": 0.0, "expected": "A模型"},
            {"name": "大负速度测试", "vel_x": -1.0, "expected": "B模型"},
        ]
        
        for i, test in enumerate(test_cases):
            self.get_logger().info(f"\n=== 测试 {i+1}: {test['name']} ===")
            self.get_logger().info(f"发送速度: vel_x={test['vel_x']}, 期望切换到: {test['expected']}")
            
            # 创建速度消息
            cmd = Twist()
            cmd.linear.x = test['vel_x']
            cmd.linear.y = 0.0
            cmd.linear.z = 0.0
            cmd.angular.x = 0.0
            cmd.angular.y = 0.0
            cmd.angular.z = 0.0
            
            # 发布速度命令
            self.vel_publisher.publish(cmd)
            self.get_logger().info(f"已发布速度命令到 /vel_cmd")
            
            # 等待一段时间观察结果
            time.sleep(2.0)
            
            # 处理回调
            rclpy.spin_once(self, timeout_sec=0.1)
        
        self.get_logger().info("\n=== 测试完成 ===")

def main():
    rclpy.init()
    
    try:
        debugger = VelocitySwitchDebugger()
        
        # 启动测试
        debugger.test_velocity_commands()
        
        # 继续监听
        debugger.get_logger().info("继续监听模型切换信号... (按Ctrl+C退出)")
        rclpy.spin(debugger)
        
    except KeyboardInterrupt:
        print("\n用户中断，退出...")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()

if __name__ == '__main__':
    main()
